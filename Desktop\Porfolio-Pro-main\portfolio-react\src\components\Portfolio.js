import { useRef, useEffect, useState, useCallback } from 'react';
import { useVisitorTracking } from '../hooks/useVisitorTracking';
import { logDebug } from '../utils/logger';

const Portfolio = () => {
  const { ref: portfolioRef } = useVisitorTracking('portfolio', {
    threshold: 0.3,
    minDuration: 4
  });
  
  // State for notification
  const [showNotification, setShowNotification] = useState(false);
  const portfolioItems = [
    { // Project 1
      href: "https://threed-e-commerce.onrender.com",
      image: "/3D E-Comm.PNG",
      alt: "3D Ecommerce",
      title: "3D Ecommerce",
      forSale: true
    },
    { // Project 2
      href: "#",
      image: "/ex1.webp",
      alt: "Will be deployed soon.",
      title: "Will be deployed soon.",
      forSale: false
    },
    { // Project 3
      href: "https://creative-website-jumper.onrender.com",
      image: "/P1.PNG",
      alt: "Nexit Brand Identity",
      title: "Professional Portfolio",
      forSale: true
    },
    { // Project 4
      href: "#",
      image: "/ex3.webp",
      alt: "Will be deployed soon.s",
      title: "Will be deployed soon.",
      forSale: false
    },
    { // Project 5
      href: "#",
      image: "/ex4.1.png",
      alt: "Will be deployed soon.",
      title: "Will be deployed soon.",
      forSale: false
    },
    { // Project 6
      href: "#",
      image: "/ex5.png",
      alt: "Will be deployed soon.",
      title: "Will be deployed soon.",
      forSale: false
    },
    { // Project 7
      href: "https://hoobank-neon-future.onrender.com",
      image: "/HooBank.png",
      alt: "Hoobank Super Technology",
      title: "Available",
      forSale: true
    }
  ];

  // Project tracking function
  const trackProjectInteraction = useCallback(async (item, interactionType = 'click', index = 0) => {
    const API_URL = process.env.REACT_APP_API_URL;
    if (!API_URL) return;

    // Generate a unique project ID based on title and index
    const projectId = `portfolio-${index}-${item.title.replace(/\s+/g, '-').toLowerCase()}`;
    const isAvailable = item.href !== "#";

    try {
      await fetch(`${API_URL}/api/track/visit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: `portfolio-item-${interactionType}`,
          duration: 1, // Minimal duration for interaction tracking
          sessionId: `${Date.now()}-${Math.random()}`,
          pageUrl: window.location.href,
          projectTitle: item.title,
          projectType: 'portfolio-carousel',
          projectId: projectId,
          projectAvailable: isAvailable,
          projectUrl: isAvailable ? item.href : '',
          interactionType: interactionType
        }),
      });

      logDebug(`📊 Portfolio Project Interaction: ${item.title} - ${interactionType}`, {
        projectId,
        available: isAvailable,
        url: item.href
      });
    } catch (error) {
      console.warn('Portfolio project interaction tracking failed:', error);
    }
  }, []);

  // Refs and state for carousel functionality
  const carouselRef = useRef(null);
  const trackRef = useRef(null);
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  // Use refs to store current state values for the animation loop
  const stateRef = useRef({
    isHovered: false,
    isDragging: false
  });

  // Update state ref whenever state changes
  useEffect(() => {
    stateRef.current = {
      isHovered,
      isDragging
    };
  }, [isHovered, isDragging]);

  // Auto-scroll functionality - NEVER STOP continuous infinite scrolling
  useEffect(() => {
    let animationId;

    const autoScroll = () => {
      const { isHovered: hovering, isDragging: dragging } = stateRef.current;

      if (trackRef.current && !hovering && !dragging) {
        const track = trackRef.current;
        const scrollAmount = 1; // Pixels per frame

        // Get actual scroll width from DOM
        const actualScrollWidth = track.scrollWidth;
        const maxScroll = actualScrollWidth / 2; // Half width (one complete set)

        // Smooth continuous scrolling
        track.scrollLeft += scrollAmount;

        // NEVER STOP - seamless infinite loop when reaching the end
        if (track.scrollLeft >= maxScroll - 10) { // Small buffer to prevent issues
          track.scrollLeft = 0; // Reset to start immediately
        }
      }

      // Continue the animation loop - NEVER STOP
      animationId = requestAnimationFrame(autoScroll);
    };

    // Start the infinite animation loop immediately
    animationId = requestAnimationFrame(autoScroll);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []); // No dependencies - run once and never stop

  // Mouse event handlers for desktop drag functionality
  const handleMouseDown = (e) => {
    if (!trackRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - trackRef.current.offsetLeft);
    setScrollLeft(trackRef.current.scrollLeft);
    trackRef.current.style.cursor = 'grabbing';
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !trackRef.current) return;
    e.preventDefault();
    const track = trackRef.current;
    const x = e.pageX - track.offsetLeft;
    const walk = (x - startX) * 2; // Multiply for faster scroll
    const newScrollLeft = scrollLeft - walk;
    const maxScroll = track.scrollWidth / 2; // Half of actual scroll width

    track.scrollLeft = newScrollLeft;

    // Handle infinite scroll boundaries for manual drag
    if (track.scrollLeft >= maxScroll) {
      track.scrollLeft = 0; // Reset to start
      setScrollLeft(0); // Update the reference point
      setStartX(x); // Update the start position
    } else if (track.scrollLeft < 0) {
      track.scrollLeft = maxScroll - 1; // Jump to end
      setScrollLeft(maxScroll - 1); // Update the reference point
      setStartX(x); // Update the start position
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    if (trackRef.current) {
      trackRef.current.style.cursor = 'grab';
    }
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
    setIsHovered(false);
    if (trackRef.current) {
      trackRef.current.style.cursor = 'grab';
    }
  };

  // Touch event handlers for mobile swipe functionality
  const handleTouchStart = (e) => {
    if (!trackRef.current) return;
    setIsDragging(true);
    const touch = e.touches[0];
    setStartX(touch.clientX);
    setScrollLeft(trackRef.current.scrollLeft);
    // Prevent default to avoid conflicts with native scrolling
    e.preventDefault();
  };

  const handleTouchMove = (e) => {
    if (!isDragging || !trackRef.current) return;
    e.preventDefault(); // Prevent page scrolling
    const touch = e.touches[0];
    const walk = (startX - touch.clientX) * 1.5; // Adjust sensitivity
    trackRef.current.scrollLeft = scrollLeft + walk;
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  // Wheel event handler for mouse wheel scrolling with infinite scroll support
  const handleWheel = useCallback((e) => {
    if (!trackRef.current) return;

    // Only handle wheel events when hovering over the carousel
    // This prevents page scrolling when interacting with the carousel
    e.preventDefault();
    e.stopPropagation();

    const track = trackRef.current;
    const scrollAmount = e.deltaY * 2; // Multiply for more responsive scrolling
    const maxScroll = track.scrollWidth / 2; // Half of actual scroll width

    track.scrollLeft += scrollAmount;

    // Handle infinite scroll boundaries for manual wheel scrolling
    if (track.scrollLeft >= maxScroll) {
      track.scrollLeft = 0; // Reset to start
    } else if (track.scrollLeft < 0) {
      track.scrollLeft = maxScroll - 1; // Jump to end
    }
  }, []);

  // Hover handlers for auto-scroll pause/resume
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeaveCarousel = () => {
    setIsHovered(false);
  };



  // Handle wheel events with proper event listener options to avoid passive event listener issues
  useEffect(() => {
    const carousel = carouselRef.current;
    if (!carousel) return;

    // Add wheel event listener with { passive: false } to allow preventDefault
    carousel.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      carousel.removeEventListener('wheel', handleWheel);
    };
  }, [handleWheel]);

  return (
    <section className="portfolio" ref={portfolioRef}>
      <h2>Top Projects<br /></h2>
      <button className="discover-button" onClick={() => logDebug('Discover more clicked')}>DISCOVER MORE</button>
      <div
        className="portfolio-carousel"
        ref={carouselRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeaveCarousel}
      >
        <div
          className="carousel-track"
          ref={trackRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
        >
          {/* Render items twice for infinite scroll */}
          {[...portfolioItems, ...portfolioItems].map((item, index) => (
            <div key={index} className="portfolio-item">
              {item.forSale && (
                <div className="for-sale-ribbon">
                  <span>Available</span>
                </div>
              )}
              {item.href === "#" ? (
                <button
                  onClick={() => {
                    trackProjectInteraction(item, 'unavailable-click', index);
                    setShowNotification(true);
                  }}
                  onMouseEnter={() => trackProjectInteraction(item, 'hover', index)}
                  style={{
                    background: 'none',
                    border: 'none',
                    padding: 0,
                    width: '100%',
                    cursor: 'pointer'
                  }}
                >
                  <img src={item.image} alt={item.alt} />
                  <p>{item.title}</p>
                </button>
              ) : (
                <a
                  href={item.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={() => trackProjectInteraction(item, 'click', index)}
                  onMouseEnter={() => trackProjectInteraction(item, 'hover', index)}
                >
                  <img src={item.image} alt={item.alt} />
                  <p>{item.title}</p>
                </a>
              )}
            </div>
          ))}
          
          {/* Notification Modal */}
          {showNotification && (
            <div className="notification-overlay" onClick={() => setShowNotification(false)}>
              <div className="notification-message" onClick={(e) => e.stopPropagation()}>
                <h3>Coming Soon!</h3>
                <p>This exciting project is currently under development. We're crafting something extraordinary that combines innovation with exceptional user experience. Stay tuned for the unveiling!</p>
                <button className="notification-close" onClick={() => setShowNotification(false)}>
                  Got it!
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
