import React from 'react';
import { useVisitorTracking } from '../hooks/useVisitorTracking';

const ClientThoughts = () => {
  const { ref } = useVisitorTracking('client-thoughts', {
    threshold: 0.5,
    minDuration: 3
  });

  return (
    <section className="client-thoughts" ref={ref}>
      <div className="quote-icon">
        {/* Quote icon placeholder */}
      </div>
      <h2>CLIENT THOUGHTS</h2>
      <h3>"E-COMMERCE EXPERTISE"</h3>
      <p>Will be Available Soon . . . </p>
      <div className="thoughts-image">
        <img 
          src="/client touch.jpg" 
          alt="E-commerce Expertise Visual" 
          style={{
            width: '100%', 
            maxWidth: '600px', 
            height: 'auto', 
            margin: '20px 0', 
            border: '2px solid #FF2D55', 
            borderRadius: '10px'
          }}
        />
      </div>
    </section>
  );
};

export default ClientThoughts;
