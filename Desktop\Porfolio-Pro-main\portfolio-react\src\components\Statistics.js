import React from 'react';
import { useVisitorTracking } from '../hooks/useVisitorTracking';

const Statistics = () => {
  const { ref } = useVisitorTracking('statistics', {
    threshold: 0.6,
    minDuration: 2
  });
  const handleLetsTalkClick = (e) => {
    e.preventDefault();

    // Find the contact section by ID first, then by class as fallback
    const contactSection = document.getElementById('contact') || document.querySelector('.contact');

    if (contactSection) {
      // Get header height dynamically
      const header = document.querySelector('header');
      const headerHeight = header ? header.offsetHeight + 20 : 100; // Add some padding

      // Calculate target position
      const contactPosition = contactSection.offsetTop;
      const targetPosition = contactPosition - headerHeight;

      // Smooth scroll to the contact section
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });

      // Alternative method using scrollIntoView for better browser compatibility
      setTimeout(() => {
        contactSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }, 50);
    }
  };
  return (
    <section className="statistics" ref={ref}>
      <h2>STATISTICS</h2>
      <div className="stats-grid">
        <div className="stat">
          <h3>9+</h3>
          <p>WEBSITES DEVELOPED</p>
        </div>
        <div className="stat">
          <h3>14</h3>
          <p>Web DESIGN PROJECTS</p>
        </div>
        <div className="stat">
          <h3>3+</h3>
          <p>YEARS OF EXPERIENCE</p>
        </div>
      </div>
      <div className="stats-image">
        <img src="/business-8398066.jpg" alt="Business statistics visualization" />
      </div>
      <button onClick={handleLetsTalkClick} className="action-button">LET'S TALK</button>
    </section>
  );
};

export default Statistics;
