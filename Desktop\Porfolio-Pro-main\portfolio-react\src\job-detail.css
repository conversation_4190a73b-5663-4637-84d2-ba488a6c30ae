/* Job Detail Pages Styles */

/* Keyframes for mobile glow effect */
@keyframes glow-pulse {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0.8;
        transform: scale(1.02);
    }
}

@keyframes rotate-glow {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes swipe-hint {
    0%, 100% {
        transform: translateX(0);
        opacity: 1;
    }
    50% {
        transform: translateX(-5px);
        opacity: 0.7;
    }
}

@keyframes swipe-feedback {
    0% {
        transform: translateX(0) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: translateX(10px) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translateX(0) scale(1);
        opacity: 0.8;
    }
}

@keyframes fade-in-out {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    20%, 80% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* Back Navigation */
.back-navigation {
    position: fixed;
    top: 120px; /* Position below header to prevent collision */
    left: 40px;
    z-index: 999; /* High z-index but below header (which is 1000) */
    background: transparent;
    padding: 0;
    margin: 0;
}

.back-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    color: #FFFFFF;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-weight: 400;
    font-size: 16px;
    transition: all 0.3s ease;
    padding: 12px 24px;
    border-radius: 25px;
    background: rgba(75, 0, 130, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(75, 0, 130, 0.3);
    cursor: pointer;
    /* Remove default button styles */
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    opacity: 1;
}

/* Transparent state when scrolled */
.back-button.scrolled {
    opacity: 0.3;
    background: rgba(75, 0, 130, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 10px rgba(75, 0, 130, 0.1);
}

/* Restore opacity on hover */
.back-button.scrolled:hover {
    opacity: 1;
    background: rgba(255, 45, 85, 0.9);
    border-color: #FF2D55;
    transform: translateX(-5px) scale(1.05);
    box-shadow: 0 6px 25px rgba(255, 45, 85, 0.4);
}

/* Hide mobile text on desktop */
.back-text-mobile {
    display: none;
}

.back-button:hover {
    background: rgba(255, 45, 85, 0.9);
    border-color: #FF2D55;
    transform: translateX(-5px) scale(1.05);
    box-shadow: 0 6px 25px rgba(255, 45, 85, 0.4);
}

.back-button:active {
    transform: translateX(-3px) scale(1.02);
}

.back-arrow {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.back-button:hover .back-arrow {
    transform: translateX(-3px);
}

/* Job Hero Section */
.job-hero {
    background: #000000;
    padding: 80px 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.job-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(75, 0, 130, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 45, 85, 0.3) 0%, transparent 50%);
    z-index: 1;
}

.job-hero-content {
    position: relative;
    z-index: 2;
    max-width: 1000px;
    margin: 0 auto;
}

.company-branding {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.hero-company-logo {
    width: 120px;
    height: 120px;
    border-radius: 20px;
    border: 3px solid #FFFFFF;
    box-shadow: 0 10px 30px rgba(75, 0, 130, 0.3);
    transition: all 0.3s ease;
}

.hero-company-logo:hover {
    transform: scale(1.05);
    border-color: #FF2D55;
    box-shadow: 0 15px 40px rgba(255, 45, 85, 0.4);
}

.company-info {
    text-align: left;
}

.job-title-hero {
    font-family: 'Montserrat', sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #FFFFFF;
    margin: 0 0 10px 0;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.company-name-hero {
    font-family: 'Montserrat', sans-serif;
    font-size: 24px;
    font-weight: 400;
    color: #FF2D55;
    margin: 0 0 10px 0;
}

.company-link-hero {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    margin: 15px 0;
    display: flex;
    justify-content: flex-start;
}

.company-link-hero a {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #4B0082;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 14px;
    padding: 12px 24px;
    border-radius: 25px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.company-link-hero a::before {
    content: "🌐";
    font-size: 16px;
}

.company-link-hero a::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.company-link-hero a:hover {
    background: linear-gradient(135deg, rgba(75, 0, 130, 0.25), rgba(255, 45, 85, 0.25));
    border-color: rgba(255, 45, 85, 0.6);
    color: #FF2D55;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 35px rgba(255, 45, 85, 0.25);
}

.company-link-hero a:hover::after {
    left: 100%;
}

.job-duration-hero {
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.job-summary {
    max-width: 800px;
    margin: 0 auto;
    padding: 30px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.job-summary p {
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    line-height: 1.6;
    color: #FFFFFF;
    margin: 0;
}

/* Job Content Section */
.job-content {
    padding: 80px 40px;
}

.content-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
    background: radial-gradient(circle at 30% 70%, rgba(75, 0, 130, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 45, 85, 0.3) 0%, transparent 50%);
}

.content-card {
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(75, 0, 130, 0.2);
    transition: all 0.3s ease;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);
    border-color: rgba(255, 45, 85, 0.3);
}

.content-card h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 28px;
    font-weight: 700;
    color: #FF2D55;
    margin: 0 0 20px 0;
    text-transform: uppercase;
}

.content-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 20px;
    font-weight: 600;
    color: #FFFFFF;
    margin: 30px 0 15px 0;
}

.content-card p {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
}

.content-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.content-card li {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.content-card li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: #FF2D55;
    font-weight: bold;
}

/* Skills Grid */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.skill-category h4 {
    color: #4B0082;
    margin-bottom: 15px;
    font-size: 18px;
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.skill-tag {
    background: linear-gradient(135deg, #4B0082, #FF2D55);
    color: #FFFFFF;
    padding: 8px 16px;
    border-radius: 20px;
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skill-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 45, 85, 0.3);
}

/* Accomplishments */
.accomplishments-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.accomplishment-item {
    text-align: center;
    padding: 30px 20px;
    backdrop-filter: blur(15px);
    border-radius: 15px;
    border: 1px solid rgba(255, 45, 85, 0.2);
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(75, 0, 130, 0.2);
}

.accomplishment-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);
    border-color: rgba(255, 45, 85, 0.4);
}

.metric {
    font-family: 'Montserrat', sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #FF2D55;
    margin-bottom: 10px;
}

.metric-description {
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

/* Role Projects Section */
.role-projects {
    padding: 80px 40px;
    background: linear-gradient(135deg, rgba(75, 0, 130, 0.1), rgba(255, 45, 85, 0.1));
}

.role-projects h2 {
    font-family: 'Montserrat', sans-serif;
    font-size: 36px;
    font-weight: 700;
    color: #FFFFFF;
    text-align: center;
    margin-bottom: 60px;
    text-transform: uppercase;
}

.projects-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
}

.project-card {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);
}

.project-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-info {
    padding: 30px;
    transition: all 0.3s ease;
    border-radius: 0 0 20px 20px;
}

.project-info:hover {
    background: rgba(75, 0, 130, 0.1);
    transform: translateY(-2px);
}

.project-info h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 22px;
    font-weight: 600;
    color: #FFFFFF;
    margin: 0 0 15px 0;
}

.project-info p {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    margin-bottom: 20px;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.project-tech span {
    background: linear-gradient(135deg, #4B0082, #FF2D55);
    color: #FFFFFF;
    padding: 5px 12px;
    border-radius: 15px;
    font-family: 'Montserrat', sans-serif;
    font-size: 12px;
    font-weight: 500;
}

/* Project Link Indicator */
.project-link {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.project-card:hover .project-link {
    opacity: 1;
    transform: translateY(0);
}

.project-link span {
    color: #FF2D55;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.project-link span::after {
    content: '🚀';
    font-size: 16px;
    transition: transform 0.3s ease;
}

.project-card:hover .project-link span::after {
    transform: translateX(3px);
}

/* Enhanced clickable project card styles */
.project-card[style*="cursor: pointer"] {
    transition: all 0.3s ease, transform 0.2s ease;
}

.project-card[style*="cursor: pointer"]:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(75, 0, 130, 0.4);
    border-color: rgba(255, 45, 85, 0.5);
}

.project-card[style*="cursor: pointer"]:active {
    transform: translateY(-12px) scale(1.01);
}



/* Mobile Responsiveness */
@media (max-width: 768px) {
    .back-navigation {
        top: 80px; /* Adjust for mobile header height */
        left: 0; /* Position at the very edge for half-circle effect */
        right: auto;
        position: fixed;
    }

    .back-button {
        padding: 12px 20px 12px 8px;
        font-size: 12px;
        width: 140px;
        height: 50px;
        /* Half-circle design - only show right half */
        border-radius: 0 25px 25px 0;
        /* Mobile swipe design styling */
        background: linear-gradient(135deg, rgba(75, 0, 130, 0.8), rgba(255, 45, 85, 0.6));
        border: 2px solid rgba(0, 255, 255, 0.4);
        border-left: none; /* Remove left border for half effect */
        box-shadow:
            0 0 15px rgba(0, 255, 255, 0.3),
            0 4px 15px rgba(75, 0, 130, 0.2);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
        /* Position it so it appears to come from the left edge */
        left: -20px;
        padding-left: 25px; /* Add padding to account for the offset */
    }

    /* Glowing effect for mobile - half circle */
    .back-button::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.2), transparent);
        border-radius: 0 27px 27px 0; /* Match the half-circle shape */
        z-index: -1;
        animation: glow-pulse 2s ease-in-out infinite alternate;
    }

    /* Additional glow ring effect - half circle */
    .back-button::after {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        background: linear-gradient(90deg,
            transparent,
            rgba(0, 255, 255, 0.1),
            rgba(75, 0, 130, 0.1),
            rgba(255, 45, 85, 0.1),
            rgba(0, 255, 255, 0.1)
        );
        border-radius: 0 29px 29px 0; /* Match the half-circle shape */
        z-index: -2;
        opacity: 0.6;
        animation: rotate-glow 4s linear infinite;
    }

    /* Hide desktop text on mobile */
    .back-text-desktop {
        display: none;
    }

    /* Show mobile text */
    .back-text-mobile {
        display: inline;
        font-weight: 500;
        text-transform: lowercase;
        letter-spacing: 0.5px;
    }

    /* Swipe indicator animation */
    .swipe-indicator {
        display: inline-block;
        margin-right: 4px;
        animation: swipe-hint 1.5s ease-in-out infinite;
        color: rgba(0, 255, 255, 0.8);
        font-weight: bold;
    }

    /* Mobile scrolled state */
    .back-button.scrolled {
        opacity: 0.4;
        background: linear-gradient(135deg, rgba(75, 0, 130, 0.4), rgba(255, 45, 85, 0.3));
        border-color: rgba(0, 255, 255, 0.2);
        box-shadow:
            0 0 8px rgba(0, 255, 255, 0.2),
            0 2px 8px rgba(75, 0, 130, 0.1);
    }

    /* Mobile hover/touch state */
    .back-button.scrolled:hover,
    .back-button.scrolled:active {
        opacity: 1;
        background: linear-gradient(135deg, rgba(75, 0, 130, 0.9), rgba(255, 45, 85, 0.7));
        border-color: rgba(0, 255, 255, 0.6);
        border-left: none; /* Maintain half-circle on hover */
        box-shadow:
            0 0 20px rgba(0, 255, 255, 0.4),
            0 6px 20px rgba(75, 0, 130, 0.3);
        transform: translateX(-2px) scale(1.02);
    }

    /* Regular hover state for mobile */
    .back-button:hover {
        background: linear-gradient(135deg, rgba(255, 45, 85, 0.9), rgba(75, 0, 130, 0.7));
        border-color: rgba(0, 255, 255, 0.8);
        border-left: none; /* Maintain half-circle on hover */
        box-shadow:
            0 0 25px rgba(0, 255, 255, 0.5),
            0 8px 25px rgba(255, 45, 85, 0.3);
        transform: translateX(-2px) scale(1.05);
    }

    /* Touch/active state for mobile */
    .back-button:active,
    .back-button.swipe-active {
        background: linear-gradient(135deg, rgba(0, 255, 255, 0.8), rgba(255, 45, 85, 0.8));
        border-color: rgba(0, 255, 255, 1);
        border-left: none;
        box-shadow:
            0 0 30px rgba(0, 255, 255, 0.7),
            0 10px 30px rgba(0, 255, 255, 0.4);
        transform: translateX(-1px) scale(1.08);
        transition: all 0.1s ease;
    }

    /* Swipe feedback animation */
    .back-button.swipe-active .swipe-indicator {
        animation: swipe-feedback 0.3s ease-in-out;
        color: rgba(0, 255, 255, 1);
    }

    /* Swipe hint text */
    .swipe-hint-text {
        position: absolute;
        top: 60px;
        left: 20px;
        background: rgba(0, 255, 255, 0.9);
        color: #000;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        animation: fade-in-out 2s ease-in-out;
        pointer-events: none;
        z-index: 1001;
    }
}

/* Tablet and medium screen adjustments */
@media (max-width: 768px) {
    .job-hero {
        padding: 60px 20px;
    }

    .company-branding {
        flex-direction: column;
        gap: 20px;
    }

    .company-info {
        text-align: center;
    }

    .job-title-hero {
        font-size: 32px;
    }

    .company-name-hero {
        font-size: 20px;
    }

    .company-link-hero a {
        font-size: 12px;
        padding: 10px 20px;
        border-radius: 20px;
    }

    .company-link-hero a::before {
        font-size: 14px;
    }

    .job-summary {
        padding: 20px;
    }

    .job-content {
        padding: 60px 20px;
    }

    .content-card {
        padding: 25px;
    }

    .content-card h3 {
        font-size: 24px;
    }

    .skills-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .accomplishments-list {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }

    .metric {
        font-size: 36px;
    }

    .role-projects {
        padding: 60px 20px;
    }

    .role-projects h2 {
        font-size: 28px;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

@media (max-width: 480px) {
    .job-title-hero {
        font-size: 24px;
    }

    .company-name-hero {
        font-size: 18px;
    }

    .company-link-hero a {
        font-size: 11px;
        padding: 8px 16px;
        border-radius: 18px;
        gap: 6px;
    }

    .company-link-hero a::before {
        font-size: 12px;
    }

    .hero-company-logo {
        width: 80px;
        height: 80px;
    }

    .content-card {
        padding: 20px;
    }

    .accomplishments-list {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    /* Extra small mobile back button adjustments */
    .back-button {
        padding: 10px 16px 10px 6px;
        font-size: 11px;
        width: 120px;
        height: 45px;
        border-radius: 0 22px 22px 0; /* Maintain half-circle for small screens */
        border-left: none;
        left: -15px;
        padding-left: 20px;
    }

    .back-arrow {
        font-size: 14px;
    }

    .back-text-mobile {
        font-size: 10px;
    }
}

/* Custom skill category styles based on category names */
.frontend_skills {
    border-left: 4px solid #61DAFB; /* React blue */
    background: rgba(97, 218, 251, 0.05);
}

.frontend_skills h4 {
    color: #61DAFB !important;
}

.styling_skills {
    border-left: 4px solid #FF6B6B; /* Coral red */
    background: rgba(255, 107, 107, 0.05);
}

.styling_skills h4 {
    color: #FF6B6B !important;
}

.tools___testing_skills {
    border-left: 4px solid #4ECDC4; /* Teal */
    background: rgba(78, 205, 196, 0.05);
}

.tools___testing_skills h4 {
    color: #4ECDC4 !important;
}

.backend_skills {
    border-left: 4px solid #68D391; /* Green */
    background: rgba(104, 211, 145, 0.05);
}

.backend_skills h4 {
    color: #68D391 !important;
}

.cloud___devops_skills {
    border-left: 4px solid #F6AD55; /* Orange */
    background: rgba(246, 173, 85, 0.05);
}

.cloud___devops_skills h4 {
    color: #F6AD55 !important;
}

.design_tools_skills {
    border-left: 4px solid #ED64A6; /* Pink */
    background: rgba(237, 100, 166, 0.05);
}

.design_tools_skills h4 {
    color: #ED64A6 !important;
}

.frontend_development_skills {
    border-left: 4px solid #9F7AEA; /* Purple */
    background: rgba(159, 122, 234, 0.05);
}

.frontend_development_skills h4 {
    color: #9F7AEA !important;
}

.ux_research_skills {
    border-left: 4px solid #38B2AC; /* Teal green */
    background: rgba(56, 178, 172, 0.05);
}

.ux_research_skills h4 {
    color: #38B2AC !important;
}

/* Enhanced skill category styling */
.skill-category {
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.03);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.skill-category:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* NDA Project Image Styling */
.project-image img[src*="NDA.jpg"] {
    filter: grayscale(20%);
    opacity: 0.9;
    border: 2px solid #FF6B6B;
    position: relative;
}

.project-card:has(img[src*="NDA.jpg"]) {
    border: 2px solid rgba(255, 107, 107, 0.3);
    background: rgba(255, 107, 107, 0.05);
}

.project-card:has(img[src*="NDA.jpg"]):hover {
    border-color: rgba(255, 107, 107, 0.6);
    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.2);
}

.project-card:has(img[src*="NDA.jpg"]) .project-info::before {
    content: "🔒 NDA Protected";
    display: inline-block;
    background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
