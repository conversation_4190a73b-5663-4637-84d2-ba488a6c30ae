require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Admin = require('../models/Admin');

const seed = async () => {
  await mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
  const email = process.env.ADMIN_EMAIL;
  const password = process.env.ADMIN_PASSWORD;
  if (!email || !password) {
    console.error('ADMIN_EMAIL and ADMIN_PASSWORD must be set in .env');
    process.exit(1);
  }
  const hashed = await bcrypt.hash(password, 10);
  let admin = await Admin.findOne({ email });
  if (!admin) {
    admin = new Admin({ email, password: hashed });
    await admin.save();
    console.log('Admin user created');
  } else {
    admin.password = hashed;
    await admin.save();
    console.log('Admin password updated');
  }
  process.exit();
};
seed(); 