<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Analytics Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #FFD700;
        }
        button {
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid rgba(255, 215, 0, 0.5);
            color: #FFD700;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 215, 0, 0.3);
            transform: translateY(-2px);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #22c55e; }
        .error { color: #ef4444; }
        .info { color: #3b82f6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Project Analytics System Test</h1>
        <p>This page tests the new project analytics functionality for both Experience and Portfolio sections.</p>

        <div class="test-section">
            <h2>📊 Experience Project Tracking Test</h2>
            <p>Test tracking interactions with experience section projects (job details).</p>
            <button onclick="testExperienceTracking()">Test Experience Project Interaction</button>
            <button onclick="fetchExperienceAnalytics()">Fetch Experience Analytics</button>
            <div id="experienceResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🎨 Portfolio Project Tracking Test</h2>
            <p>Test tracking interactions with portfolio carousel projects.</p>
            <button onclick="testPortfolioTracking()">Test Portfolio Project Interaction</button>
            <button onclick="testUnavailableProject()">Test Unavailable Project Click</button>
            <button onclick="fetchPortfolioAnalytics()">Fetch Portfolio Analytics</button>
            <div id="portfolioResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🔧 System Status</h2>
            <button onclick="checkBackendStatus()">Check Backend Status</button>
            <button onclick="testAdminLogin()">Test Admin Login</button>
            <div id="statusResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:5000';
        let adminToken = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            return `[${timestamp}] ${message}`;
        }

        function displayResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const logMessage = log(message, type);
            element.innerHTML += `<span class="${type}">${logMessage}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        async function checkBackendStatus() {
            const resultId = 'statusResult';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                displayResult(resultId, 'Checking backend status...', 'info');
                const response = await fetch(`${API_URL}/`);
                const text = await response.text();
                displayResult(resultId, `✅ Backend is running: ${text}`, 'success');
            } catch (error) {
                displayResult(resultId, `❌ Backend connection failed: ${error.message}`, 'error');
            }
        }

        async function testAdminLogin() {
            const resultId = 'statusResult';
            
            try {
                displayResult(resultId, 'Testing admin login...', 'info');
                const response = await fetch(`${API_URL}/api/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>', // Replace with actual admin email
                        password: 'admin123' // Replace with actual admin password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    adminToken = data.token;
                    displayResult(resultId, '✅ Admin login successful', 'success');
                } else {
                    displayResult(resultId, '⚠️ Admin login failed - using test mode', 'error');
                }
            } catch (error) {
                displayResult(resultId, `❌ Admin login error: ${error.message}`, 'error');
            }
        }

        async function testExperienceTracking() {
            const resultId = 'experienceResult';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                displayResult(resultId, 'Testing experience project tracking...', 'info');
                
                const trackingData = {
                    section: 'experience-item-click',
                    duration: 1,
                    sessionId: `test-${Date.now()}`,
                    pageUrl: window.location.href,
                    jobTitle: 'Frontend Developer Angular',
                    jobSlug: 'frontend-receeto',
                    projectTitle: 'Frontend Developer Angular',
                    projectType: 'experience',
                    projectId: 'experience-1-frontend-receeto',
                    projectAvailable: true,
                    projectUrl: '/job/frontend-receeto',
                    interactionType: 'click'
                };

                const response = await fetch(`${API_URL}/api/track/visit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(trackingData)
                });

                if (response.ok) {
                    displayResult(resultId, '✅ Experience tracking successful', 'success');
                    displayResult(resultId, `📊 Tracked: ${trackingData.jobTitle}`, 'info');
                } else {
                    displayResult(resultId, `❌ Experience tracking failed: ${response.status}`, 'error');
                }
            } catch (error) {
                displayResult(resultId, `❌ Experience tracking error: ${error.message}`, 'error');
            }
        }

        async function testPortfolioTracking() {
            const resultId = 'portfolioResult';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                displayResult(resultId, 'Testing portfolio project tracking...', 'info');
                
                const trackingData = {
                    section: 'portfolio-item-click',
                    duration: 1,
                    sessionId: `test-${Date.now()}`,
                    pageUrl: window.location.href,
                    projectTitle: '3D Ecommerce',
                    projectType: 'portfolio-carousel',
                    projectId: 'portfolio-0-3d-ecommerce',
                    projectAvailable: true,
                    projectUrl: 'https://threed-e-commerce.onrender.com',
                    interactionType: 'click'
                };

                const response = await fetch(`${API_URL}/api/track/visit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(trackingData)
                });

                if (response.ok) {
                    displayResult(resultId, '✅ Portfolio tracking successful', 'success');
                    displayResult(resultId, `📊 Tracked: ${trackingData.projectTitle}`, 'info');
                } else {
                    displayResult(resultId, `❌ Portfolio tracking failed: ${response.status}`, 'error');
                }
            } catch (error) {
                displayResult(resultId, `❌ Portfolio tracking error: ${error.message}`, 'error');
            }
        }

        async function testUnavailableProject() {
            const resultId = 'portfolioResult';
            
            try {
                displayResult(resultId, 'Testing unavailable project tracking...', 'info');
                
                const trackingData = {
                    section: 'portfolio-item-unavailable-click',
                    duration: 1,
                    sessionId: `test-${Date.now()}`,
                    pageUrl: window.location.href,
                    projectTitle: 'Will be deployed soon.',
                    projectType: 'portfolio-carousel',
                    projectId: 'portfolio-1-will-be-deployed-soon.',
                    projectAvailable: false,
                    projectUrl: '#',
                    interactionType: 'unavailable-click'
                };

                const response = await fetch(`${API_URL}/api/track/visit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(trackingData)
                });

                if (response.ok) {
                    displayResult(resultId, '✅ Unavailable project tracking successful', 'success');
                    displayResult(resultId, `📊 Tracked unavailable: ${trackingData.projectTitle}`, 'info');
                } else {
                    displayResult(resultId, `❌ Unavailable project tracking failed: ${response.status}`, 'error');
                }
            } catch (error) {
                displayResult(resultId, `❌ Unavailable project tracking error: ${error.message}`, 'error');
            }
        }

        async function fetchExperienceAnalytics() {
            const resultId = 'experienceResult';
            
            try {
                displayResult(resultId, 'Fetching experience analytics...', 'info');
                
                if (!adminToken) {
                    displayResult(resultId, '⚠️ No admin token - please login first', 'error');
                    return;
                }

                const response = await fetch(`${API_URL}/api/admin/experience-projects-analytics`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult(resultId, '✅ Experience analytics fetched successfully', 'success');
                    displayResult(resultId, `📊 Total projects: ${data.totalProjects}`, 'info');
                    displayResult(resultId, `📊 Total views: ${data.summary.totalViews}`, 'info');
                    displayResult(resultId, JSON.stringify(data, null, 2), 'info');
                } else {
                    displayResult(resultId, `❌ Experience analytics failed: ${response.status}`, 'error');
                }
            } catch (error) {
                displayResult(resultId, `❌ Experience analytics error: ${error.message}`, 'error');
            }
        }

        async function fetchPortfolioAnalytics() {
            const resultId = 'portfolioResult';
            
            try {
                displayResult(resultId, 'Fetching portfolio analytics...', 'info');
                
                if (!adminToken) {
                    displayResult(resultId, '⚠️ No admin token - please login first', 'error');
                    return;
                }

                const response = await fetch(`${API_URL}/api/admin/portfolio-projects-analytics`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult(resultId, '✅ Portfolio analytics fetched successfully', 'success');
                    displayResult(resultId, `📊 Total projects: ${data.totalProjects}`, 'info');
                    displayResult(resultId, `📊 Available: ${data.summary.availableProjectsCount}`, 'info');
                    displayResult(resultId, `📊 Unavailable: ${data.summary.unavailableProjectsCount}`, 'info');
                    displayResult(resultId, JSON.stringify(data, null, 2), 'info');
                } else {
                    displayResult(resultId, `❌ Portfolio analytics failed: ${response.status}`, 'error');
                }
            } catch (error) {
                displayResult(resultId, `❌ Portfolio analytics error: ${error.message}`, 'error');
            }
        }

        // Auto-check backend status on page load
        window.onload = function() {
            checkBackendStatus();
        };
    </script>
</body>
</html>
