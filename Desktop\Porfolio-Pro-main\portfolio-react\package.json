{"name": "med-amine-chouchane-portfolio", "version": "0.1.0", "private": true, "homepage": "https://porfolio-pro.onrender.com", "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "express": "^4.18.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "recharts": "^3.0.2", "swiper": "^11.2.8", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "serve": "node server.js", "heroku-postbuild": "npm run build", "predeploy": "npm run build", "deploy": "gh-pages -d build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"gh-pages": "^6.1.1"}}