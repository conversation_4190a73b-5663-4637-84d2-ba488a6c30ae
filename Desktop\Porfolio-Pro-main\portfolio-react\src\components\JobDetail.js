import React, { useState, useCallback, useEffect, lazy, Suspense } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { jobsData } from '../data/jobsData';
import Header from './Header';
import Footer from './Footer';
import '../job-detail.css';
import './ProjectImageSwiper.css';
import { useJobDetailTracking, useVisitorTracking } from '../hooks/useVisitorTracking';
import { logDebug, warnDebug } from '../utils/logger';

// Lazy load heavy components
const ProjectImageSwiper = lazy(() => import('./ProjectImageSwiper'));
const NDANotification = lazy(() => import('./NDANotification'));

const JobDetail = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const job = jobsData.find(job => job.slug === slug);

  // Track job detail page viewing
  useJobDetailTracking(job?.title || 'Unknown Job', slug);

  // Track individual sections within job detail
  const { ref: heroRef } = useVisitorTracking(`job-detail-hero-${slug}`, {
    threshold: 0.5,
    minDuration: 3,
    trackOnMount: true
  });

  const { ref: roleOverviewRef } = useVisitorTracking(`job-detail-role-overview-${slug}`, {
    threshold: 0.6,
    minDuration: 5
  });

  const { ref: skillsRef } = useVisitorTracking(`job-detail-skills-${slug}`, {
    threshold: 0.5,
    minDuration: 4
  });

  const { ref: accomplishmentsRef } = useVisitorTracking(`job-detail-accomplishments-${slug}`, {
    threshold: 0.6,
    minDuration: 3
  });

  const { ref: projectsRef } = useVisitorTracking(`job-detail-projects-${slug}`, {
    threshold: 0.4,
    minDuration: 6
  });

  const [ndaNotification, setNdaNotification] = useState({ isOpen: false, projectTitle: '' });
  const [isScrolled, setIsScrolled] = useState(false);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [isSwipeActive, setIsSwipeActive] = useState(false);

  // Scroll to top when component mounts or slug changes
  useEffect(() => {
    // Scroll to the very top of the page
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });

    // Alternative method for better browser compatibility
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
  }, [slug]); // Re-run when slug changes (different job selected)

  // Handle scroll events for back button transparency
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setIsScrolled(scrollTop > 100); // Make transparent after scrolling 100px
    };

    window.addEventListener('scroll', handleScroll);

    // Check initial scroll position
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);



  // Smart back navigation handler
  const handleBackNavigation = useCallback((e) => {
    e.preventDefault();

    // Get the stored scroll position for this job
    const storedPosition = sessionStorage.getItem(`timeline-scroll-${slug}`);

    // Navigate back to home with experience section
    navigate('/', { replace: true });

    // Wait for navigation to complete, then scroll to the stored position
    setTimeout(() => {
      if (storedPosition) {
        const position = parseInt(storedPosition, 10);
        window.scrollTo({
          top: position,
          behavior: 'smooth'
        });
      } else {
        // Fallback: scroll to experience section
        const experienceSection = document.querySelector('.experience');
        if (experienceSection) {
          experienceSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    }, 100);
  }, [slug, navigate]);

  // Swipe detection functions
  const minSwipeDistance = 50; // Minimum distance for a swipe
  const edgeThreshold = 50; // Distance from left edge to detect swipe

  const handleTouchStart = useCallback((e) => {
    setTouchEnd(null); // Reset touch end
    const touchX = e.targetTouches[0].clientX;
    setTouchStart(touchX);

    // Show visual feedback if starting from left edge
    if (touchX <= edgeThreshold) {
      setIsSwipeActive(true);
    }
  }, [edgeThreshold]);

  const handleTouchMove = useCallback((e) => {
    const currentX = e.targetTouches[0].clientX;
    setTouchEnd(currentX);

    // Show swipe feedback if moving right from left edge
    if (touchStart && touchStart <= edgeThreshold && currentX > touchStart + 20) {
      setIsSwipeActive(true);
    }
  }, [touchStart, edgeThreshold]);

  const handleTouchEnd = useCallback(() => {
    setIsSwipeActive(false); // Reset visual feedback

    if (!touchStart || !touchEnd) return;

    const distance = touchEnd - touchStart; // Positive = right swipe, negative = left swipe
    const isRightSwipe = distance > minSwipeDistance;
    const startedFromEdge = touchStart <= edgeThreshold; // Started from left edge

    // If user swipes right from the left edge OR swipes right on the button, trigger back navigation
    if (isRightSwipe && (startedFromEdge || touchStart <= 200)) {
      handleBackNavigation({ preventDefault: () => {} }); // Mock event object
    }
  }, [touchStart, touchEnd, minSwipeDistance, edgeThreshold, handleBackNavigation]);

  // Add global swipe detection for mobile
  useEffect(() => {
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
      // Add touch event listeners to the entire document for swipe detection
      document.addEventListener('touchstart', handleTouchStart, { passive: true });
      document.addEventListener('touchmove', handleTouchMove, { passive: true });
      document.addEventListener('touchend', handleTouchEnd, { passive: true });

      return () => {
        document.removeEventListener('touchstart', handleTouchStart);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd]);

  // Memoized NDA check
  const isNDAProject = useCallback((project) => {
    return project.description.toLowerCase().includes('nda') ||
           project.title.toLowerCase().includes('nda') ||
           project.images.some(img => img.includes('NDA'));
  }, []);

  // Memoized handler
  const handleProjectInfoClick = useCallback((e, project) => {
    e.stopPropagation();
    if (isNDAProject(project)) {
      setNdaNotification({ isOpen: true, projectTitle: project.title });
      return;
    }
    window.open(project.liveUrl, '_blank');
  }, [isNDAProject]);

  const closeNdaNotification = useCallback(() => {
    setNdaNotification({ isOpen: false, projectTitle: '' });
  }, []);

  // Track project card interactions
  const trackProjectInteraction = useCallback(async (projectTitle, interactionType = 'view') => {
    const API_URL = process.env.REACT_APP_API_URL;
    if (!API_URL) return;

    try {
      await fetch(`${API_URL}/api/track/visit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: `project-card-${interactionType}`,
          duration: 1, // Minimal duration for interaction tracking
          sessionId: `${Date.now()}-${Math.random()}`,
          pageUrl: window.location.href,
          projectTitle: projectTitle,
          jobTitle: job?.title,
          jobSlug: slug
        }),
      });

      logDebug(`📊 Tracked project interaction: ${projectTitle} - ${interactionType}`);
    } catch (error) {
      warnDebug('Project interaction tracking failed:', error);
    }
  }, [job?.title, slug]);

  // Track content card interactions (Technologies & Skills)
  const trackContentCardInteraction = useCallback(async (cardTitle, skillCategory = '') => {
    const API_URL = process.env.REACT_APP_API_URL;
    if (!API_URL) return;

    try {
      await fetch(`${API_URL}/api/track/visit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'content-card-interaction',
          duration: 1,
          sessionId: `${Date.now()}-${Math.random()}`,
          pageUrl: window.location.href,
          cardTitle: cardTitle,
          skillCategory: skillCategory,
          jobTitle: job?.title,
          jobSlug: slug
        }),
      });

      logDebug(`📊 Tracked content card: ${cardTitle} - ${skillCategory}`);
    } catch (error) {
      warnDebug('Content card tracking failed:', error);
    }
  }, [job?.title, slug]);

  if (!job) {
    return (
      <div>
        <Header />
        <div style={{ padding: '100px 20px', textAlign: 'center', color: 'white' }}>
          <h1>Job Not Found</h1>
          <Link to="/" style={{ color: '#4B0082' }}>← Back to Home</Link>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Header />
      
      {/* Navigation Back */}
      <div className="back-navigation">
        <button
          onClick={handleBackNavigation}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          className={`back-button ${isScrolled ? 'scrolled' : ''} ${isSwipeActive ? 'swipe-active' : ''}`}
        >
          <span className="back-arrow">←</span>
          <span className="back-text-desktop">Back to Timeline</span>
          <span className="back-text-mobile">
            <span className="swipe-indicator">←</span>
            Swipe to back
          </span>
        </button>

        {/* Swipe hint for mobile */}
        {isSwipeActive && (
          <div className="swipe-hint-text">
            Swipe right to go back →
          </div>
        )}
      </div>

      {/* Job Detail Hero Section */}
      <section className="job-hero" ref={heroRef}>
        <div className="job-hero-content">
          <div className="company-branding">
            <img 
              src={job.logo} 
              alt={job.logoAlt} 
              className="hero-company-logo" 
              loading="lazy"
            />
            <div className="company-info">
              <h1 className="job-title-hero">{job.title}</h1>
              <h2 className="company-name-hero">{job.company}</h2>
              {job.companyLink && (
                <p className="company-link-hero">
                  <a href={job.companyLink} target="_blank" rel="noopener noreferrer">
                    {job.companyLink}
                  </a>
                </p>
              )}
              <p className="job-duration-hero">{job.duration}</p>
            </div>
          </div>
          <div className="job-summary">
            <p>{job.summary}</p>
          </div>
        </div>
      </section>

      {/* Job Details Content */}
      <section className="job-content">
        <div className="content-grid">
          {/* Full Job Description */}
          <div className="content-card" ref={roleOverviewRef}>
            <h3>Role Overview</h3>
            <p>{job.roleOverview}</p>

            <h4>Key Responsibilities</h4>
            <ul>
              {job.responsibilities.map((responsibility, index) => (
                <li key={index}>{responsibility}</li>
              ))}
            </ul>
          </div>

          {/* Skills & Technologies */}
          <div className="content-card" ref={skillsRef}>
            <h3>Technologies & Skills</h3>
            <div className="skills-grid">
              {Object.entries(job.skills).map(([category, skills]) => {
                // Generate class name based on category name
                const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';
                return (
                  <div
                    key={category}
                    className={`skill-category ${categoryClass}`}
                    onClick={() => trackContentCardInteraction('Technologies & Skills', category)}
                    style={{ cursor: 'pointer' }}
                    title={`Click to track viewing ${category} skills`}
                  >
                    <h4>{category}</h4>
                    <div className="skill-tags">
                      {skills.map((skill, index) => (
                        <span key={index} className="skill-tag">{skill}</span>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Key Accomplishments */}
          <div className="content-card" ref={accomplishmentsRef}>
            <h3>Key Accomplishments</h3>
            <div className="accomplishments-list">
              {job.accomplishments.map((accomplishment, index) => (
                <div key={index} className="accomplishment-item">
                  <div className="metric">{accomplishment.metric}</div>
                  <div className="metric-description">{accomplishment.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Project Portfolio from this role */}
      <section className="role-projects" ref={projectsRef}>
        <h2>Projects from this Role</h2>
        <div className="projects-grid">
          {job.projects.map((project, index) => (
            <div
              key={index}
              className="project-card"
              onClick={() => trackProjectInteraction(project.title, 'view')}
              onMouseEnter={() => trackProjectInteraction(project.title, 'hover')}
              style={{ cursor: 'pointer' }}
              title={`Click to track viewing project: ${project.title}`}
            >
              <div className="project-image">
                <Suspense fallback={<div style={{minHeight: 200}}>Loading images...</div>}>
                  <ProjectImageSwiper
                    images={project.images}
                    title={project.title}
                    isNDA={isNDAProject(project)}
                    onImageInteraction={(imageIndex) =>
                      trackProjectInteraction(`${project.title} - Image ${imageIndex + 1}`, 'image-view')
                    }
                  />
                </Suspense>
              </div>
              <div
                className="project-info"
                onClick={(e) => {
                  handleProjectInfoClick(e, project);
                  trackProjectInteraction(project.title, 'click');
                }}
                style={{ cursor: 'pointer' }}
              >
                <h3>{project.title}</h3>
                <p>{project.description}</p>
                <div className="project-tech">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      onClick={(e) => {
                        e.stopPropagation();
                        trackProjectInteraction(`${project.title} - Technology: ${tech}`, 'tech-click');
                      }}
                      style={{ cursor: 'pointer' }}
                      title={`Click to track technology: ${tech}`}
                    >{tech}</span>
                  ))}
                </div>
                {project.liveUrl && (
                  <div className="project-link">
                    <span>
                      {isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* NDA Notification Modal */}
      <Suspense fallback={null}>
        <NDANotification
          isOpen={ndaNotification.isOpen}
          onClose={closeNdaNotification}
          projectTitle={ndaNotification.projectTitle}
        />
      </Suspense>

      <Footer />
    </div>
  );
};

export default JobDetail;
