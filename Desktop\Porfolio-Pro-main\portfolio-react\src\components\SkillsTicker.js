import React from 'react';
import { useVisitorTracking } from '../hooks/useVisitorTracking';

const SkillsTicker = () => {
  const { ref } = useVisitorTracking('skills-ticker', {
    threshold: 0.7,
    minDuration: 2
  });

  return (
    <div className="skills-ticker" ref={ref}>
      <div className="ticker-track">
        <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>
        {/* Duplicate for infinite scroll */}
        <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>
      </div>
    </div>
  );
};

export default SkillsTicker;
