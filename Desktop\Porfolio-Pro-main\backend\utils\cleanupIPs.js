const mongoose = require('mongoose');
const Visit = require('../models/Visit');
require('dotenv').config();

/**
 * Cleanup script to fix IP addresses that contain multiple comma-separated values
 * This happens when x-forwarded-for header contains multiple proxy IPs
 */
async function cleanupCommaIPs() {
  try {
    console.log('🔧 Starting IP cleanup process...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Find all visits with comma-separated IPs
    const visitsWithCommaIPs = await Visit.find({
      ip: { $regex: /,/ }
    });

    console.log(`📊 Found ${visitsWithCommaIPs.length} visits with comma-separated IPs`);

    if (visitsWithCommaIPs.length === 0) {
      console.log('✅ No cleanup needed - all IPs are clean');
      return;
    }

    // Process each visit
    let updatedCount = 0;
    for (const visit of visitsWithCommaIPs) {
      const originalIP = visit.ip;
      // Extract the first IP (original client IP)
      const cleanIP = originalIP.split(',')[0].trim();
      
      console.log(`🔄 Updating IP: "${originalIP}" → "${cleanIP}"`);
      
      // Update the visit
      await Visit.findByIdAndUpdate(visit._id, { ip: cleanIP });
      updatedCount++;
    }

    console.log(`✅ Successfully updated ${updatedCount} visits`);
    console.log('🎉 IP cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error during IP cleanup:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  cleanupCommaIPs();
}

module.exports = cleanupCommaIPs;
