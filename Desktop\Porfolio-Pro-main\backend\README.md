# Portfolio Backend API

## Overview
Node.js + Express.js + MongoDB backend for personal portfolio (admin-only).

## Features
- Admin login (JWT, bcrypt)
- Visitor tracking (IP, timestamp, viewed sections)
- Admin dashboard stats

## Setup
1. Copy `.env.example` to `.env` and fill in your values.
2. Install dependencies: `npm install`
3. Seed admin user: `node utils/seedAdmin.js`
4. Start server: `npm run dev` or `npm start`

## API Endpoints

### Admin Login
- `POST /api/admin/login`
  - Body: `{ "email": "...", "password": "..." }`
  - Returns: `{ token: "JWT_TOKEN" }`

### Visitor Tracking
- `POST /api/track/visit`
  - Body: `{ "section": "Home" }`
  - Logs the visitor's IP, timestamp, and section viewed.
  - Call this from React when a section/component becomes visible.

### Admin Dashboard
- `GET /api/admin/dashboard`
  - Header: `Authorization: Bearer <JWT_TOKEN>`
  - Returns:
    - `totalVisits`: number
    - `visits`: array of `{ ip, timestamp, section }`
    - `sectionStats`: array of `{ section, count, percent }`

## JWT Protection
- Send JWT as `Authorization: Bearer <token>` header for protected routes.
- Only the admin can access dashboard endpoints.

## Frontend Usage
- Call `/api/track/visit` from React when a section is viewed (e.g., via useEffect + IntersectionObserver).
- Use `/api/admin/login` to get JWT for dashboard access.
- Use `/api/admin/dashboard` to fetch stats for the admin dashboard.

## Environment Variables
Create a `.env` file in the backend directory based on the provided `.env.example`:

```
MONGO_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD_HASH=your_bcrypt_hashed_password
```

- Use bcrypt (10+ salt rounds) to hash your admin password. You can generate a hash with Node.js:
  ```js
  const bcrypt = require('bcrypt');
  bcrypt.hash('yourpassword', 12).then(console.log);
  ```

## Security Features
- JWT authentication with 1h expiry
- bcrypt password hashing
- Rate limiting on login and all routes
- Input sanitization with express-validator
- Helmet and CORS for API hardening
- HTTPS required in production
- No detailed error messages in production
- Logs failed login attempts with IP and timestamp

## Seeding Admin User
Run `node utils/seedAdmin.js` after setting your `.env` to create/update the admin user.

---

*This backend is for personal/admin use only. No public registration or user endpoints are exposed.*
