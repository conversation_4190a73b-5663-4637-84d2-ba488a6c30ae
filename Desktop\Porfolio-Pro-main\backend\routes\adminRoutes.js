const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const auth = require('../middlewares/auth');
const rateLimit = require('express-rate-limit');
const { body } = require('express-validator');

// Rate limiter for login route
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 login requests per windowMs
  message: { message: 'Too many login attempts, please try again later.' },
  standardHeaders: true,
  legacyHeaders: false,
});

router.post(
  '/login',
  loginLimiter, // Re-enabled for security
  [
    body('email').isEmail().normalizeEmail(),
    body('password').isString().trim().isLength({ min: 6, max: 128 })
  ],
  adminController.login
);
router.get('/dashboard', auth, adminController.dashboard);
router.post('/geolocation', auth, adminController.getGeolocation);
router.get('/experience-projects-analytics', auth, adminController.getExperienceProjectsAnalytics);
router.get('/portfolio-projects-analytics', auth, adminController.getPortfolioProjectsAnalytics);

module.exports = router;