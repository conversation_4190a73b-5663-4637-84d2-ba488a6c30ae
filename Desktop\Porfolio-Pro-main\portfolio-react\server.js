const express = require('express');
const path = require('path');
const app = express();

// Serve static files from the React app build directory
app.use(express.static(path.join(__dirname, 'build')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Specific route handlers for debugging
app.get('/job/*', (req, res) => {
  console.log(`Job route requested: ${req.path}`);
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

app.get('/admin/*', (req, res) => {
  console.log(`Admin route requested: ${req.path}`);
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

// Catch all handler: send back React's index.html file for any non-API routes
app.get('*', (req, res) => {
  console.log(`Catch-all route requested: ${req.path}`);
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

const port = process.env.PORT || 5000;
app.listen(port, () => {
  console.log(`React app serving on port ${port}`);
  console.log(`Server ready to handle SPA routing`);
});
