import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrow<PERSON>eft, FaUser, FaClock, FaEye, FaChartBar, FaGlobe } from 'react-icons/fa';
import { formatDuration } from '../hooks/useVisitorTracking';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { logSensitive } from '../utils/logger';
import './SectionDetailsAnalysis.css';

const SectionDetailsAnalysis = () => {
  const [detailsData, setDetailsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedVisitor, setSelectedVisitor] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchSectionDetails = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No token found. Please log in.');
        navigate('/admin/login');
        return;
      }

      // Wake up backend before fetching data
      await preemptiveWakeup();

      try {
        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/dashboard`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const dashboardData = await response.json();

        // Process dashboard data to create section details
        const processedData = {
          success: true,
          data: [],
          totalVisitors: 0
        };

        // Group visits by IP
        const visitorMap = {};
        dashboardData.visits.forEach(visit => {
          if (!visitorMap[visit.ip]) {
            visitorMap[visit.ip] = {
              ip: visit.ip,
              totalTimeSpent: 0,
              sections: {}
            };
          }

          if (!visitorMap[visit.ip].sections[visit.section]) {
            visitorMap[visit.ip].sections[visit.section] = {
              section: visit.section,
              totalDuration: 0,
              visitCount: 0,
              avgDuration: 0,
              lastVisit: visit.timestamp,
              pageUrls: []
            };
          }

          const section = visitorMap[visit.ip].sections[visit.section];
          section.totalDuration += visit.duration || 0;
          section.visitCount += 1;
          section.avgDuration = section.totalDuration / section.visitCount;
          section.lastVisit = new Date(visit.timestamp) > new Date(section.lastVisit) ? visit.timestamp : section.lastVisit;

          visitorMap[visit.ip].totalTimeSpent += visit.duration || 0;
        });

        // Convert to array format
        processedData.data = Object.values(visitorMap).map(visitor => ({
          ...visitor,
          sections: Object.values(visitor.sections).sort((a, b) => b.totalDuration - a.totalDuration)
        })).sort((a, b) => b.totalTimeSpent - a.totalTimeSpent);

        processedData.totalVisitors = processedData.data.length;

        setDetailsData(processedData);
      } catch (error) {
        logSensitive('Section details fetch error:', error);
        setError('Error fetching section details: ' + error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSectionDetails();
  }, [navigate]);

  const handleBackToDashboard = () => {
    navigate('/admin/dashboard');
  };

  const handleVisitorClick = (visitor) => {
    setSelectedVisitor(selectedVisitor?.ip === visitor.ip ? null : visitor);
  };

  const handleSectionClick = (sectionName) => {
    // Map section names to portfolio URLs
    const sectionUrls = {
      'header': '/#header',
      'intro': '/#intro',
      'intro-crafting': '/#intro-crafting',
      'skills': '/#skills',
      'statistics': '/#statistics',
      'experience': '/#experience',
      'portfolio': '/#portfolio',
      'client-thoughts': '/#client-thoughts',
      'contact': '/#contact',
      'job-detail': '/job/3d-ecommerce-platform-ui-ux-designer'
    };

    const url = sectionUrls[sectionName] || '/';
    const fullUrl = window.location.origin + url;

    // Open in new window
    window.open(fullUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="section-details-container">
        <div className="section-details-loading">Loading detailed analytics...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="section-details-container">
        <div className="section-details-error">{error}</div>
        <button onClick={handleBackToDashboard} className="back-to-dashboard-btn">
          <FaArrowLeft /> Back to Dashboard
        </button>
      </div>
    );
  }

  return (
    <div className="section-details-container">
      <div className="section-details-header">
        <button onClick={handleBackToDashboard} className="back-to-dashboard-btn">
          <FaArrowLeft /> Back to Dashboard
        </button>
        <h1 className="section-details-title">
          <FaChartBar /> Detailed Section Analysis
        </h1>
        <div className="section-details-summary">
          <span className="summary-item">
            <FaUser /> {detailsData?.totalVisitors || 0} Unique Visitors
          </span>
        </div>
      </div>

      <div className="visitors-analysis-grid">
        {detailsData?.data && detailsData.data.length > 0 ? (
          detailsData.data.map((visitor, index) => (
            <div key={visitor.ip} className="visitor-analysis-card">
              <div 
                className="visitor-header"
                onClick={() => handleVisitorClick(visitor)}
              >
                <div className="visitor-info">
                  <FaGlobe className="visitor-icon" />
                  <span className="visitor-ip">IP: {visitor.ip}</span>
                  <span className="visitor-total-time">
                    <FaClock /> Total: {formatDuration(visitor.totalTimeSpent)}
                  </span>
                </div>
                <div className="visitor-sections-count">
                  <FaEye /> {visitor.sections.length} sections viewed
                </div>
              </div>

              {selectedVisitor?.ip === visitor.ip && (
                <div className="visitor-sections-details">
                  <h4>Section Breakdown:</h4>
                  {visitor.sections.map((section, sectionIndex) => (
                    <div key={sectionIndex} className="section-detail-item">
                      <div className="section-detail-header">
                        <span
                          className="section-name clickable-section-name"
                          onClick={() => handleSectionClick(section.section)}
                          title={`Click to visit ${section.section} section`}
                        >
                          {section.section}
                        </span>
                        <span className="section-time">
                          {formatDuration(section.totalDuration)}
                        </span>
                      </div>
                      <div className="section-detail-stats">
                        <span className="section-visits">
                          {section.visitCount} visit{section.visitCount !== 1 ? 's' : ''}
                        </span>
                        <span className="section-avg">
                          Avg: {formatDuration(section.avgDuration)}
                        </span>
                        <span className="section-last-visit">
                          Last: {new Date(section.lastVisit).toLocaleDateString()}
                        </span>
                      </div>
                      {section.pageUrls && section.pageUrls.length > 0 && (
                        <div className="section-page-urls">
                          <strong>Pages visited:</strong>
                          {section.pageUrls.map((url, urlIndex) => (
                            <div key={urlIndex} className="page-url">
                              {url.includes('/job/') ? 
                                `Job Detail: ${url.split('/job/')[1]}` : 
                                url.split('/').pop() || 'Home'
                              }
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="no-data-message">
            <FaChartBar />
            <h3>No detailed analytics available</h3>
            <p>Visitor tracking data will appear here once users start interacting with your portfolio.</p>
          </div>
        )}
      </div>

      {detailsData?.data && detailsData.data.length > 0 && (
        <div className="analysis-insights">
          <h3><FaChartBar /> Key Insights</h3>
          <div className="insights-grid">
            <div className="insight-card">
              <h4>Most Engaged Visitor</h4>
              <p>
                IP: {detailsData.data[0]?.ip} - {formatDuration(detailsData.data[0]?.totalTimeSpent)}
              </p>
            </div>
            <div className="insight-card">
              <h4>Average Sections per Visitor</h4>
              <p>
                {(detailsData.data.reduce((sum, v) => sum + v.sections.length, 0) / detailsData.data.length).toFixed(1)} sections
              </p>
            </div>
            <div className="insight-card">
              <h4>Most Popular Section</h4>
              <p>
                {(() => {
                  const sectionCounts = {};
                  detailsData.data.forEach(visitor => {
                    visitor.sections.forEach(section => {
                      sectionCounts[section.section] = (sectionCounts[section.section] || 0) + section.totalDuration;
                    });
                  });
                  const topSection = Object.entries(sectionCounts).sort((a, b) => b[1] - a[1])[0];
                  return topSection ? `${topSection[0]} (${formatDuration(topSection[1])})` : 'N/A';
                })()}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SectionDetailsAnalysis;
