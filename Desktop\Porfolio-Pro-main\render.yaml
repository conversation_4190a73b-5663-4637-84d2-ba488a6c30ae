services:
  # Backend API Service
  - type: web
    name: porfolio-pro-backend
    env: node
    buildCommand: cd backend && npm install
    startCommand: cd backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGO_URI
        fromDatabase:
          name: portfolio-db
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: ADMIN_EMAIL
        sync: false
      - key: ADMIN_PASSWORD_HASH
        sync: false

  # Frontend React App Service
  - type: web
    name: porfolio-pro-frontend
    env: node
    buildCommand: cd portfolio-react && npm install && npm run build
    startCommand: cd portfolio-react && npm run serve
    envVars:
      - key: NODE_ENV
        value: production

databases:
  - name: portfolio-db
    databaseName: portfolio
    user: portfolio_user
