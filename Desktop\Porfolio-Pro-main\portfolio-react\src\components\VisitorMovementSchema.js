import React, { useState, useEffect } from 'react';
import { <PERSON>aRoute, FaClock, FaEye, FaArrowRight, FaPlay, FaPause } from 'react-icons/fa';
import './VisitorMovementSchema.css';

const VisitorMovementSchema = ({ visitorData }) => {
  const [animationSpeed, setAnimationSpeed] = useState(2000); // 2 seconds per step
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [movementPath, setMovementPath] = useState([]);

  useEffect(() => {
    if (visitorData && visitorData.visits) {
      // Create movement path from visit history
      const sortedVisits = visitorData.visits
        .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
        .map((visit, index) => ({
          ...visit,
          step: index + 1,
          timestamp: new Date(visit.timestamp),
          duration: visit.duration || 0
        }));
      
      setMovementPath(sortedVisits);
    }
  }, [visitorData]);

  useEffect(() => {
    let interval;
    if (isPlaying && movementPath.length > 0) {
      interval = setInterval(() => {
        setCurrentStep(prev => {
          if (prev >= movementPath.length - 1) {
            setIsPlaying(false);
            return 0;
          }
          return prev + 1;
        });
      }, animationSpeed);
    }
    return () => clearInterval(interval);
  }, [isPlaying, animationSpeed, movementPath.length]);

  const formatDuration = (seconds) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getSectionColor = (section) => {
    const colors = {
      'header': '#FF2D55',
      'about': '#007AFF',
      'skills': '#34C759',
      'projects': '#FF9500',
      'timeline': '#AF52DE',
      'contact': '#FF3B30',
      'footer': '#8E8E93'
    };
    return colors[section.toLowerCase()] || '#5AC8FA';
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleStepClick = (stepIndex) => {
    setCurrentStep(stepIndex);
    setIsPlaying(false);
  };

  const handleSpeedChange = (speed) => {
    setAnimationSpeed(speed);
  };

  if (!visitorData || !movementPath.length) {
    return (
      <div className="visitor-movement-schema">
        <div className="schema-header">
          <h2><FaRoute /> Visitor Movement Schema</h2>
        </div>
        <div className="no-movement-data">
          <p>No movement data available for this visitor.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="visitor-movement-schema">
      <div className="schema-header">
        <h2><FaRoute /> Visitor Movement Schema</h2>
        <div className="schema-controls">
          <button 
            className={`play-pause-btn ${isPlaying ? 'playing' : 'paused'}`}
            onClick={handlePlayPause}
          >
            {isPlaying ? <FaPause /> : <FaPlay />}
            {isPlaying ? 'Pause' : 'Play'}
          </button>
          <div className="speed-controls">
            <span>Speed:</span>
            <button 
              className={animationSpeed === 3000 ? 'active' : ''}
              onClick={() => handleSpeedChange(3000)}
            >
              Slow
            </button>
            <button 
              className={animationSpeed === 2000 ? 'active' : ''}
              onClick={() => handleSpeedChange(2000)}
            >
              Normal
            </button>
            <button 
              className={animationSpeed === 1000 ? 'active' : ''}
              onClick={() => handleSpeedChange(1000)}
            >
              Fast
            </button>
          </div>
        </div>
      </div>

      <div className="schema-stats">
        <div className="stat-item">
          <FaEye />
          <span>Total Steps: {movementPath.length}</span>
        </div>
        <div className="stat-item">
          <FaClock />
          <span>Total Time: {formatDuration(visitorData.totalDuration)}</span>
        </div>
        <div className="stat-item">
          <FaRoute />
          <span>Sections Visited: {visitorData.sectionsVisited.length}</span>
        </div>
      </div>

      <div className="movement-timeline">
        <div className="timeline-progress">
          <div 
            className="progress-bar"
            style={{ 
              width: `${((currentStep + 1) / movementPath.length) * 100}%` 
            }}
          />
        </div>

        <div className="movement-steps">
          {movementPath.map((step, index) => (
            <div 
              key={index}
              className={`movement-step ${index <= currentStep ? 'active' : ''} ${index === currentStep ? 'current' : ''}`}
              onClick={() => handleStepClick(index)}
            >
              <div className="step-number">{step.step}</div>
              <div 
                className="step-section"
                style={{ borderColor: getSectionColor(step.section) }}
              >
                <div className="section-name">{step.section}</div>
                <div className="section-time">
                  {step.timestamp.toLocaleTimeString()}
                </div>
                <div className="section-duration">
                  <FaClock /> {formatDuration(step.duration)}
                </div>
              </div>
              {index < movementPath.length - 1 && (
                <div className="step-arrow">
                  <FaArrowRight />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="current-step-details">
        {movementPath[currentStep] && (
          <div className="step-details-card">
            <h3>Step {currentStep + 1} Details</h3>
            <div className="details-grid">
              <div className="detail-item">
                <span className="detail-label">Section:</span>
                <span 
                  className="detail-value section-badge"
                  style={{ backgroundColor: getSectionColor(movementPath[currentStep].section) }}
                >
                  {movementPath[currentStep].section}
                </span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Time:</span>
                <span className="detail-value">
                  {movementPath[currentStep].timestamp.toLocaleString()}
                </span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Duration:</span>
                <span className="detail-value">
                  {formatDuration(movementPath[currentStep].duration)}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VisitorMovementSchema;
